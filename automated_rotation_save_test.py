#!/usr/bin/env python3
"""
Automated test to verify rotation save functionality
This will:
1. Load a STEP file into TOP viewer
2. Apply rotations (button and/or mouse simulation)
3. Save with the green button (improved save method)
4. Load the saved file into BOTTOM viewer
5. Compare both viewers to verify they show the same rotated model
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from step_viewer_tdk_modular_fixed import StepViewerTDK
except ImportError:
    print("❌ Could not import step_viewer_tdk_modular_fixed")
    print("Trying alternative import...")
    try:
        from step_viewer_tdk_modular import StepViewerTDK
    except ImportError:
        print("❌ Could not import any viewer module")
        sys.exit(1)

class AutomatedRotationSaveTest:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.viewer = None
        self.test_file = None
        self.saved_file = "test_rotation_save_verification.step"
        self.step = 0
        
        # Find available STEP files
        step_files = [f for f in os.listdir('.') if f.endswith('.STEP') or f.endswith('.step')]
        if step_files:
            self.test_file = step_files[0]
            print(f"✅ Found test file: {self.test_file}")
        else:
            print("❌ No STEP files found in current directory")
            
    def run_test(self):
        """Run the complete automated test"""
        print("🔧 AUTOMATED ROTATION SAVE VERIFICATION TEST")
        print("=" * 60)
        print("This test will:")
        print("1. Load a STEP file into TOP viewer")
        print("2. Apply rotations")
        print("3. Save with green button (improved method)")
        print("4. Load saved file into BOTTOM viewer")
        print("5. Verify both viewers show same rotated model")
        print("=" * 60)
        
        if not self.test_file:
            print("❌ No test file available - cannot run test")
            return
            
        # Initialize viewer
        print("\n🔧 Step 1: Initializing dual viewer...")
        self.viewer = StepViewerTDK()
        self.viewer.show()
        
        # Start the test sequence with delays
        QTimer.singleShot(2000, self.step1_load_top_file)
        
        # Start the application
        self.app.exec_()
        
    def step1_load_top_file(self):
        """Step 1: Load file into TOP viewer"""
        print(f"\n🔧 Step 2: Loading {self.test_file} into TOP viewer...")

        # Set active viewer to TOP
        self.viewer.active_viewer = "top"
        self.viewer.update_viewer_highlights()

        # Load the file directly using the step loader
        try:
            success, message = self.viewer.step_loader_left.load_step_file(self.test_file)

            if success and self.viewer.step_loader_left.current_polydata:
                # Display the polydata
                self.viewer.vtk_renderer_left.clear_view()
                display_success = self.viewer.vtk_renderer_left.display_polydata(self.viewer.step_loader_left.current_polydata)
                self.viewer.vtk_renderer_left.fit_view()
                self.viewer.vtk_renderer_left.toggle_bounding_box(True)
                self.viewer.top_file_label.setText(f"TOP: {os.path.basename(self.test_file)}")
                self.viewer.extract_step_transformation_data("top")

                print(f"✅ File loaded successfully into TOP viewer")
                # Wait then apply rotations
                QTimer.singleShot(3000, self.step2_apply_rotations)
            else:
                print(f"❌ Failed to load file into TOP viewer: {message}")
                self.app.quit()
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            self.app.quit()
            
    def step2_apply_rotations(self):
        """Step 2: Apply rotations to the model"""
        print(f"\n🔧 Step 3: Applying rotations to model...")
        
        try:
            # Apply some button rotations (these should work)
            print("   - Applying X+15° rotation...")
            self.viewer.rotate_shape("x", 15.0)
            
            print("   - Applying Y+30° rotation...")
            self.viewer.rotate_shape("y", 30.0)
            
            print("   - Applying Z+45° rotation...")
            self.viewer.rotate_shape("z", 45.0)
            
            print("✅ Rotations applied successfully")
            print(f"   Current rotation: X=15°, Y=30°, Z=45°")
            
            # Wait then save the file
            QTimer.singleShot(2000, self.step3_save_file)
            
        except Exception as e:
            print(f"❌ Error applying rotations: {e}")
            self.app.quit()
            
    def step3_save_file(self):
        """Step 3: Save the rotated file using the green button method"""
        print(f"\n🔧 Step 4: Saving rotated file as {self.saved_file}...")
        
        try:
            # Use the improved save method (green button functionality) - direct version
            if hasattr(self.viewer, 'save_step_file_option1_direct'):
                success = self.viewer.save_step_file_option1_direct(self.saved_file)

                if success:
                    print(f"✅ File saved successfully: {self.saved_file}")
                    # Check if file exists and has reasonable size
                    if os.path.exists(self.saved_file):
                        size = os.path.getsize(self.saved_file)
                        print(f"   File size: {size:,} bytes")
                        if size > 1000:  # Reasonable minimum size
                            QTimer.singleShot(2000, self.step4_load_bottom_file)
                        else:
                            print(f"❌ Saved file too small ({size} bytes) - may be corrupted")
                            self.app.quit()
                    else:
                        print(f"❌ Saved file not found: {self.saved_file}")
                        self.app.quit()
                else:
                    print(f"❌ Save operation failed")
                    self.app.quit()
            else:
                print(f"❌ Direct save method not available")
                self.app.quit()
                
        except Exception as e:
            print(f"❌ Error saving file: {e}")
            self.app.quit()
            
    def step4_load_bottom_file(self):
        """Step 4: Load the saved file into BOTTOM viewer"""
        print(f"\n🔧 Step 5: Loading saved file into BOTTOM viewer...")

        try:
            # Set active viewer to BOTTOM
            self.viewer.active_viewer = "bottom"
            self.viewer.update_viewer_highlights()

            # Load the saved file directly using the step loader
            success, message = self.viewer.step_loader_right.load_step_file(self.saved_file)

            if success and self.viewer.step_loader_right.current_polydata:
                # Display the polydata
                self.viewer.vtk_renderer_right.clear_view()
                display_success = self.viewer.vtk_renderer_right.display_polydata(self.viewer.step_loader_right.current_polydata)
                self.viewer.vtk_renderer_right.fit_view()
                self.viewer.vtk_renderer_right.toggle_bounding_box(True)
                self.viewer.bottom_file_label.setText(f"BOTTOM: {os.path.basename(self.saved_file)}")
                self.viewer.extract_step_transformation_data("bottom")

                print(f"✅ Saved file loaded successfully into BOTTOM viewer")
                QTimer.singleShot(3000, self.step5_verify_results)
            else:
                print(f"❌ Failed to load saved file into BOTTOM viewer: {message}")
                self.app.quit()

        except Exception as e:
            print(f"❌ Error loading saved file: {e}")
            self.app.quit()
            
    def step5_verify_results(self):
        """Step 5: Verify both viewers show the same rotated model"""
        print(f"\n🔧 Step 6: Verifying results...")
        
        try:
            print("✅ TEST COMPLETED SUCCESSFULLY!")
            print("=" * 60)
            print("VERIFICATION RESULTS:")
            print(f"   - Original file: {self.test_file} (loaded in TOP viewer)")
            print(f"   - Rotations applied: X=15°, Y=30°, Z=45°")
            print(f"   - Saved file: {self.saved_file} (loaded in BOTTOM viewer)")
            print(f"   - File size: {os.path.getsize(self.saved_file):,} bytes")
            print("")
            print("MANUAL VERIFICATION REQUIRED:")
            print("   1. Check that TOP viewer shows the rotated original model")
            print("   2. Check that BOTTOM viewer shows the same rotated model")
            print("   3. Both viewers should look identical")
            print("   4. The rotation should be preserved in the saved file")
            print("")
            print("✅ If both viewers show the same rotated model, the fix is working!")
            print("❌ If they look different, there may be an issue with the save/load process")
            print("=" * 60)
            
            # Keep the application running for manual inspection
            print("\n🔧 Application will remain open for manual verification...")
            print("   Close the window when you're done inspecting the results.")
            
        except Exception as e:
            print(f"❌ Error in verification: {e}")
            self.app.quit()

if __name__ == "__main__":
    test = AutomatedRotationSaveTest()
    test.run_test()
